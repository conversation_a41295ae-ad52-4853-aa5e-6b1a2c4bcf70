import type { AppRouteModule } from '@/router/types'
import { LAYOUT } from '@/router/constant'

const operation: AppRouteModule = {
  path: '/operation',
  name: 'Operation',
  component: LAYOUT,
  redirect: '/operation/history',
  meta: {
    title: '运营管理',
    icon: 'ion:settings-outline'
  },
  children: [
    {
      path: 'history',
      name: 'HistoryData',
      component: LAYOUT,
      redirect: '/operation/history/store',
      meta: {
        title: '历史数据',
        icon: 'ion:time-outline'
      },
      children: [
        {
          path: 'store',
          name: 'OperationStoreHistory',
          component: () => import('@/views/operation/history/store/index.vue'),
          meta: {
            title: '门店历史数据',
            icon: 'ion:storefront-outline'
          }
        }
      ]
    }
  ]
}

export default operation

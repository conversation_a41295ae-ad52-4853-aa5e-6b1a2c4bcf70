import request from '@/config/axios'
import type { BaseParams, PageResult } from '@/api/types'

// 门店历史数据接口
export interface StoreHistoryData {
  date: string
  code: string
  totalOrders: number
  totalIncome: number
  avgOrderValue: number
  meituanOrders: number
  meituanIncome: number
  meituanAvgValue: number
  meituanRating: number
  eleOrders: number
  eleIncome: number
  eleAvgValue: number
  eleRating: number
  jdOrders: number
  jdIncome: number
  jdAvgValue: number
  jdRating: number
}

// 平台历史数据接口
export interface PlatformHistoryData {
  date: string
  code: string
  orders: number
  income: number
  avgValue: number
  rating: number
  promotionCost: number
  exposure: number
  visitRate: number
  newCustomerOrders: number
  repeatCustomerOrders: number
  repeat7Days: number
  repeat30Days: number
  actualPayment: number
  businessHours: number
}

// 历史数据查询参数
export interface HistoryQueryParams extends BaseParams {
  deptId?: number | null
  userId?: number | null
  brandId?: number | null
  cityId?: number | null
  storeId?: number | null
  categoryId?: number | null
  startDate?: string
  endDate?: string
  pageNo?: number
  pageSize?: number
}

// API接口
export const getAllStoreHistoryData = (params: HistoryQueryParams) => {
  return request<PageResult<StoreHistoryData>>({
    url: '/operation/history/store/all',
    method: 'get',
    params
  })
}

export const getMeituanStoreHistoryData = (params: HistoryQueryParams) => {
  return request<PageResult<PlatformHistoryData>>({
    url: '/operation/history/store/meituan',
    method: 'get',
    params
  })
}

export const getEleStoreHistoryData = (params: HistoryQueryParams) => {
  return request<PageResult<PlatformHistoryData>>({
    url: '/operation/history/store/ele',
    method: 'get',
    params
  })
}

export const getJdStoreHistoryData = (params: HistoryQueryParams) => {
  return request<PageResult<PlatformHistoryData>>({
    url: '/operation/history/store/jd',
    method: 'get',
    params
  })
}

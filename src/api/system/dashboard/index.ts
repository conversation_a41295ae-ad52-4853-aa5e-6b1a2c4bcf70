import request from '@/config/axios'

// 数据统计相关的接口类型定义
export interface DashboardStatsVO {
  orderCount: number
  dispatchCount: number
  completedCount: number
  totalAmount: number
}

export interface CompareStatsVO {
  dayCompare: string
  dispatchCompare: string
  mergeAmount: number
}

export interface PendingStatsVO {
  dayPending: string
  dispatchPending: string
  pendingAmount: number
}

export interface MonthStatsVO {
  monthCompare: string
  monthDispatch: string
  monthAmount: number
}

export interface TargetDataVO {
  label: string
  value: string
  unit: string
  color: string
}

// 左侧统计数据接口
export interface LeftStatsVO {
  pendingOnline: number    // 待上线
  online: number          // 已上线
  businessCount: number   // 营业数
}

// 实施目标单量接口
export interface TargetOrderVO {
  targetOrders: number    // 目标单量
  currentOrders: number   // 当前单量
  completionRate: string  // 完成率
}

// 运营人员数据接口
export interface OperationStaffVO {
  staffName: string       // 运营人员
  businessRate: string    // 营业率
  fullOpenRate: string    // 全开率
}

// 筛选条件接口
export interface DashboardFilterVO {
  deptId?: number | undefined
  userId?: number | undefined
  brandId?: number | undefined
  dataTime: number
}

// 仪表板 API
export const DashboardApi = {

  // 刷新仪表板数据
  refreshDashboardData: async () => {
    return await request.post({ url: '/system/dashboard/refresh' })
  },

  // 实时单量数据
  realtimeOrderData: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/get-real-order-count', params })
  },

  // 运营实时数据-运营人员维度
  operationUesrRealtimeOrderData: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/operation-user/real-order-count', params })
  },

  // 获取品牌数据列表
  getBrandRealtimeOrderData: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/brand/real-order-count', params })
  },

  // 获取统计数据
  getTodayStats: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/today-stats', params })
  },

  getCompareStats: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/compare-stats', params })
  },

  getPendingStats: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/pending-stats', params })
  },

  getMonthStats: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/month-stats', params })
  },

  // 获取目标数据
  getOperationTargetData: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/operation-target', params })
  },

  getBrandTargetData: async (params: DashboardFilterVO) => {
    return await request.get({ url: '/system/dashboard/brand-target', params })
  },

  // 获取表格数据
  getTableDataPage: async (params: DashboardFilterVO & PageParam) => {
    return await request.get({ url: '/system/dashboard/table-data', params })
  },

  // // 获取左侧统计数据
  // getLeftStats: async (params: DashboardFilterVO) => {
  //   return await request.get({ url: '/system/dashboard/left-stats', params })
  // },

  // 获取实施目标单量数据
  // getTargetOrderData: async (params: DashboardFilterVO) => {
  //   return await request.get({ url: '/system/dashboard/target-order', params })
  // },

  // // 获取运营人员数据
  // getOperationStaffData: async (params: DashboardFilterVO) => {
  //   return await request.get({ url: '/system/dashboard/operation-staff', params })
  // },

  getSummaryData: async (params: DashboardFilterVO) => {
    return await request.get({url: '/system/dashboard/summary-data',params})
  }

}

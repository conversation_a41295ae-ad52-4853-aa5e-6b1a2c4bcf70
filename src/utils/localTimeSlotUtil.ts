export class LocalTimeSlotUtil {
  private static readonly START_TIME = 9 * 60; // 9:00 in minutes
  private static readonly END_TIME = 20 * 60;   // 20:00 in minutes
  private static readonly INTERVAL_MINUTES = 30;

  /**
   * Calculate time slot start
   * @param currentDateTime - The current date time
   * @returns The calculated time slot start
   */
  static calculateTimeSlotStart(currentDateTime?: Date): Date {
    if (!currentDateTime) {
      currentDateTime = new Date();
    }

    // Convert current time to minutes since midnight
    const currentTimeMinutes = currentDateTime.getHours() * 60 + currentDateTime.getMinutes();
    let resultTimeMinutes: number;

    if (currentTimeMinutes < this.START_TIME) {
      resultTimeMinutes = this.START_TIME;
    } else if (currentTimeMinutes > this.END_TIME) {
      resultTimeMinutes = this.END_TIME;
    } else {
      const minutesSinceStart = currentTimeMinutes - this.START_TIME;
      const intervalsPassed = Math.floor(minutesSinceStart / this.INTERVAL_MINUTES);
      resultTimeMinutes = this.START_TIME + intervalsPassed * this.INTERVAL_MINUTES;
    }

    // Create new date with the calculated time
    const resultDate = new Date(currentDateTime);
    const hours = Math.floor(resultTimeMinutes / 60);
    const minutes = resultTimeMinutes % 60;
    resultDate.setHours(hours, minutes, 0, 0); // Set hours, minutes, seconds, milliseconds

    return resultDate;
  }
}
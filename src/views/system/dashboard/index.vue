<template>
  <div class="dashboard-container">
    <!-- 顶部筛选条件 -->
    <ContentWrap>
      <div class="mb-4">
        <div class="bg-white rounded-lg p-4 shadow-sm border">
          <div class="flex justify-between items-center">
            <!-- <div class="text-sm">{{ dateTime }}</div> -->
            <div class="text-sm text-gray-500">{{ lastUpdateTime }}</div>
            <div class="flex items-center space-x-8">
              <!-- 运营部门选择 -->
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">运营部门:</span>
                <el-tree-select v-model="filterParams.deptId" :data="deptList"
                  :props="{ label: 'name', value: 'id', children: 'children' }" placeholder="全部部门" clearable
                  class="w-48" @change="handleFilterChange" check-strictly :render-after-expand="false"
                  :value-type="'number'" />
              </div>

              <!-- 人员选择 -->
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">人员:</span>
                <el-select v-model="filterParams.userId" placeholder="全部人员" clearable class="w-32"
                  @change="handleFilterChange">
                  <el-option v-for="user in userList" :key="user.id" :label="user.nickname" :value="user.id" />
                </el-select>
              </div>

              <!-- 品牌选择 -->
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">品牌:</span>
                <el-select v-model="filterParams.brandId" placeholder="全部品牌" clearable class="w-32"
                  @change="handleFilterChange">
                  <el-option v-for="brand in brandList" :key="brand.id" :label="brand.name" :value="brand.id" />
                </el-select>
              </div>

              <!-- 刷新按钮 -->
              <el-button type="primary" :icon="RefreshIcon" @click="refreshData" :loading="loading">
                刷新
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </ContentWrap>

    <!-- 主体内容区域 -->
    <el-row :gutter="20">
      <!-- 左侧区域 -->
      <el-col :span="6">
        <!-- 左侧统计卡片 -->
        <ContentWrap>
          <div class="mb-4">
            <div class="bg-white rounded-lg p-4 shadow-sm border">
              <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div class="text-2xl font-bold text-blue-600">{{ leftStats.pendingOnline }}</div>
                  <div class="text-sm text-gray-500">待上线</div>
                </div>
                <div>
                  <div class="text-2xl font-bold text-green-600">{{ leftStats.online }}</div>
                  <div class="text-sm text-gray-500">已上线</div>
                </div>
                <div>
                  <div class="text-2xl font-bold text-orange-600">{{ leftStats.businessCount }}</div>
                  <div class="text-sm text-gray-500">营业数</div>
                </div>
              </div>
            </div>
          </div>
        </ContentWrap>

        <!-- 实施目标单量 -->
        <ContentWrap>
          <div class="mb-4">
            <div class="bg-white rounded-lg p-4 shadow-sm border">
              <h3 class="text-lg font-semibold mb-4">实施目标单量</h3>
              <div class="space-y-3">
                <div class="flex justify-between items-center">
                  <span class="text-gray-600">目标单量:</span>
                  <span class="font-semibold text-blue-600">{{ targetOrder.targetOrders }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-600">当前单量:</span>
                  <span class="font-semibold text-green-600">{{ targetOrder.currentOrders }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span class="text-gray-600">完成率:</span>
                  <span class="font-semibold text-orange-600">{{ targetOrder.completionRate }}</span>
                </div>
              </div>
            </div>
          </div>
        </ContentWrap>

        <!-- 运营人员数据列表 -->
        <ContentWrap>
          <div class="mb-4">
            <div class="bg-white rounded-lg p-4 shadow-sm border">
              <h3 class="text-lg font-semibold mb-4">运营人员数据</h3>
              <el-table :data="operationStaffList" v-loading="leftLoading" stripe style="width: 100%">
                <el-table-column prop="staffName" label="运营人员" align="center" />
                <el-table-column prop="businessRate" label="营业率" align="center" />
                <el-table-column prop="fullOpenRate" label="全开率" align="center" />
              </el-table>
            </div>
          </div>
        </ContentWrap>
      </el-col>

      <!-- 右侧区域 -->
      <el-col :span="18">
        <!-- 单量汇总 -->
        <ContentWrap>
          <div class="mb-4">
            <div class="bg-white rounded-lg p-4 shadow-sm border w-full">
              <el-table :data="summaryData" v-loading="loading" border stripe style="width: 100%">
                <el-table-column prop="orderCount" label="总单量" align="center" />
                <el-table-column prop="lastDayOrderCount" label="环比" align="center" />
                <el-table-column prop="lastWeekOrderCount" label="同比" align="center" />
                <el-table-column prop="settleAmount" label="预计收入" align="center" />
                <el-table-column prop="lastDaySettleAmount" label="环比" align="center" />
                <el-table-column prop="lastWeekSettleAmount" label="同比" align="center" />
                <el-table-column prop="avgOrderValue" :formatter="(row) => erpPriceInputFormatter(row.avgOrderValue)"
                  label="总客单" align="center" />
                <el-table-column prop="lastAvgOrderValue" label="环比" align="center" />
                <el-table-column prop="lastWeekAvgOrderValue" label="同比" align="center" />
                <el-table-column prop="storeAvgOrderCount"
                  :formatter="(row) => erpPriceInputFormatter(row.storeAvgOrderCount)" label="均单" align="center" />
                <el-table-column prop="lastDayStoreAvgOrderCount" label="环比" align="center" />
                <el-table-column prop="lastWeekStoreAvgOrderCount" label="同比" align="center" />
              </el-table>
            </div>
          </div>
        </ContentWrap>

        <!-- 数据表格 -->
        <ContentWrap>
          <div class="mb-4">
            <div class="p-4 border-b flex justify-between items-center">
              <div class="flex items-center space-x-4">
                <h3 class="text-lg font-semibold">数据统计</h3>
                <!-- 标签切换 -->
                <el-segmented v-model="activeTab" :options="tabOptions" />
              </div>
            </div>

            <!-- 运营数据表格 -->
            <el-table v-if="activeTab === 'operation'" v-loading="tableLoading" :data="operationTableData" stripe
              style="width: 100%" :show-overflow-tooltip="true">
              <el-table-column prop="operationUserName" label="运营" align="center" />
              <el-table-column prop="meiTuanOrderCount" label="美单量" align="center" />
              <el-table-column prop="meiTuanLastDayOrderCount" label="环比" align="center" />
              <el-table-column prop="meiTuanLastWeekOrderCount" label="同比" align="center" />
              <el-table-column prop="meiTuanAvgOrderValue" label="客单" align="center" />
              <el-table-column prop="meiTuanAvgStoreOrderCount" label="均单" align="center" />
              <el-table-column prop="eleOrderCount" label="饿单量" align="center" />
              <el-table-column prop="eleLastDayOrderCount" label="环比" align="center" />
              <el-table-column prop="eleLastWeekOrderCount" label="同比" align="center" />
              <el-table-column prop="eleAvgOrderValue" label="客单" align="center" />
              <el-table-column prop="eleAvgStoreOrderCount" label="均单" align="center" />
              <el-table-column prop="jdOrderCount" label="京单量" align="center" />
              <el-table-column prop="jdLastDayOrderCount" label="环比" align="center" />
              <el-table-column prop="jdLastWeekOrderCount" label="同比" align="center" />
              <el-table-column prop="jdAvgOrderValue" label="客单" align="center" />
              <el-table-column prop="jdAvgStoreOrderCount" label="均单" align="center" />
            </el-table>

            <!-- 品牌数据表格 -->
            <el-table v-else v-loading="tableLoading" :data="brandTableData" stripe style="width: 100%"
              :show-overflow-tooltip="true">
              <el-table-column prop="brandName" label="品牌" align="center" />
              <el-table-column prop="meiTuanOrderCount" label="美单量" align="center" />
              <el-table-column prop="meiTuanLastDayOrderCount" label="环比" align="center" />
              <el-table-column prop="meiTuanLastWeekOrderCount" label="同比" align="center" />
              <el-table-column prop="meiTuanAvgOrderValue" label="客单" align="center" />
              <el-table-column prop="meiTuanAvgStoreOrderCount" label="均单" align="center" />
              <el-table-column prop="eleOrderCount" label="饿单量" align="center" />
              <el-table-column prop="eleLastDayOrderCount" label="环比" align="center" />
              <el-table-column prop="eleLastWeekOrderCount" label="同比" align="center" />
              <el-table-column prop="eleAvgOrderValue" label="客单" align="center" />
              <el-table-column prop="eleAvgStoreOrderCount" label="均单" align="center" />
              <el-table-column prop="jdOrderCount" label="京单量" align="center" />
              <el-table-column prop="jdLastDayOrderCount" label="环比" align="center" />
              <el-table-column prop="jdLastWeekOrderCount" label="同比" align="center" />
              <el-table-column prop="jdAvgOrderValue" label="客单" align="center" />
              <el-table-column prop="jdAvgStoreOrderCount" label="均单" align="center" />
            </el-table>
          </div>
        </ContentWrap>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import { DashboardApi, type DashboardFilterVO, type TableDataVO, type LeftStatsVO, type TargetOrderVO, type OperationStaffVO } from '@/api/system/dashboard'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'
import * as BrandApi from '@/api/system/brand'
import { Refresh as RefreshIcon } from '@element-plus/icons-vue'
import { handleTree } from '@/utils/tree'
import { erpPriceInputFormatter } from '@/utils'
import { LocalTimeSlotUtil } from '@/utils/localTimeSlotUtil'

/** 数据统计仪表板 */
defineOptions({ name: 'SystemDashboard' })

const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中

// 响应式数据
const tableLoading = ref(false)
const leftLoading = ref(false)
const activeTab = ref('operation')
const lastUpdateTime = ref('')

// 筛选条件
const filterParams = reactive<DashboardFilterVO>({
  deptId: null,
  userId: null,
  brandId: null,
  dataTime: ''
})
function sanitizeFilterParams(params: any) {
  function toNull(val: any) {
    return (
      val === null ||
      val === undefined ||
      val === '' ||
      val === 'null' ||
      val === 'undefined'
    )
      ? null
      : Number(val)
  }
  return {
    ...params,
    deptId: toNull(params.deptId),
    userId: toNull(params.userId),
    brandId: toNull(params.brandId),
  }
}

// 标签选项
const tabOptions = [
  { label: '运营数据', value: 'operation' },
  { label: '品牌数据', value: 'brand' }
]

// 下拉选择框数据
const deptList = ref<DeptApi.DeptVO[]>([])
const userList = ref<UserApi.UserVO[]>([])
const brandList = ref<BrandApi.brandVO[]>([])

// 汇总数据
const summaryData = ref<any[]>([])

// 左侧数据
const leftStats = ref<LeftStatsVO>({
  pendingOnline: 0,
  online: 0,
  businessCount: 0
})

const targetOrder = ref<TargetOrderVO>({
  targetOrders: 0,
  currentOrders: 0,
  completionRate: '0%'
})

const operationStaffList = ref<OperationStaffVO[]>([])

// 表格数据
const operationTableData = ref<any[]>([])
const brandTableData = ref<any[]>([])

/** 获取筛选选项数据 */
const getFilterOptions = async () => {
  try {
    // 获取部门列表
    const depts = await DeptApi.getSimpleDeptList()
    deptList.value = handleTree(depts)

    // 获取用户列表（只在有有效 deptId 时传递，否则传 undefined 或不传）
    const sanitized = sanitizeFilterParams(filterParams)
    userList.value = await UserApi.getSimpleUserListByDeptId(sanitized.deptId)

    // 获取品牌列表
    brandList.value = await BrandApi.getSimpleBrandList()
  } catch (error) {
    console.error('获取筛选选项失败:', error)
  }
}

/** 获取左侧统计数据 */
const getLeftStats = async () => {
  try {
    // filterParams.dataTime = getCurrentDateTime()
    // const data = await DashboardApi.getLeftStats(filterParams)
    // leftStats.value = data || { pendingOnline: 0, online: 0, businessCount: 0 }
  } catch (error) {
    console.error('获取左侧统计数据失败:', error)
    message.error('获取统计数据失败')
  }
}

/** 获取实施目标单量数据 */
const getTargetOrderData = async () => {
  try {
    // filterParams.dataTime = getCurrentDateTime()
    // const data = await DashboardApi.getTargetOrderData(filterParams)
    // targetOrder.value = data || { targetOrders: 0, currentOrders: 0, completionRate: '0%' }
  } catch (error) {
    console.error('获取目标单量数据失败:', error)
    message.error('获取目标数据失败')
  }
}

/** 获取运营人员数据 */
const getOperationStaffData = async () => {
  leftLoading.value = true
  try {
    // filterParams.dataTime = getCurrentDateTime()
    // const data = await DashboardApi.getOperationStaffData(filterParams)
    // operationStaffList.value = data || []
  } catch (error) {
    console.error('获取运营人员数据失败:', error)
    message.error('获取运营人员数据失败')
  } finally {
    leftLoading.value = false
  }
}

/** 筛选条件变化处理 */
const handleFilterChange = () => {
  // 当筛选条件变化时，重新获取所有数据
  refreshData()
}

/** 获取实时订单数据 */
const getSummaryData = async () => {
  loading.value = true
  try {

    const res = await DashboardApi.getSummaryData(sanitizeFilterParams(filterParams))
    console.log("getSummaryData", res)
    // 保证 summaryData.value 一定为数组，防止 el-table 报错
    if (Array.isArray(res)) {
      summaryData.value = res
    } else if (res && typeof res === 'object') {
      summaryData.value = [res]
    } else {
      summaryData.value = []
    }
  } catch (error) {
    console.error('getSummaryData:', error)
  } finally {
    loading.value = false
  }
}

/** 获取运营数据 */
const getOperationData = async () => {
  try {
    // 设置当前时间
    filterParams.dataTime = new Date()
    const data = await DashboardApi.operationUesrRealtimeOrderData(sanitizeFilterParams(filterParams))
    console.log("operationUesrRealtimeOrderData", data)
    operationTableData.value = data || []
  } catch (error) {
    console.error('获取运营数据失败:', error)
    message.error('获取运营数据失败')
  }
}

/** 获取品牌数据 */
const getBrandData = async () => {
  try {
    // 设置当前时间
    filterParams.dataTime = new Date()
    const data = await DashboardApi.getBrandRealtimeOrderData(sanitizeFilterParams(filterParams))
    console.log("getBrandRealtimeOrderData", data)
    brandTableData.value = data || []
  } catch (error) {
    console.error('获取品牌数据失败:', error)
    message.error('获取品牌数据失败')
  }
}

/** 获取表格数据 */
const getTableData = async () => {
  tableLoading.value = true
  try {
    if (activeTab.value === 'operation') {
      await getOperationData()
    } else {
      await getBrandData()
    }
  } finally {
    tableLoading.value = false
  }
}

/** 刷新数据 */
const refreshData = async () => {
  lastUpdateTime.value = formatDate(LocalTimeSlotUtil.calculateTimeSlotStart(new Date()));
  await Promise.all([
    getFilterOptions(),
    getSummaryData(),
    getTableData(),
    getLeftStats(),
    getTargetOrderData(),
    getOperationStaffData()
  ])
  message.success('数据已刷新')
}

// 监听标签切换
watch(activeTab, () => {
  getTableData()
})

/** 初始化 */
onMounted(async () => {
  lastUpdateTime.value = formatDate(LocalTimeSlotUtil.calculateTimeSlotStart(new Date()));
  await Promise.all([
    getFilterOptions(),
    getSummaryData(),
    getTableData(),
    getLeftStats(),
    getTargetOrderData(),
    getOperationStaffData()
  ])
})
</script>

<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="门店编码" prop="code">
        <el-input v-model="queryParams.code" placeholder="请输入门店编码" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="品牌" prop="brandId">
        <el-input v-model="queryParams.brandId" placeholder="请输入品牌Id" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="门店名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入门店名称" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>

      <el-form-item label="区域" prop="areaName">
        <el-input v-model="queryParams.areaName" placeholder="请输入区域名称" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="运营部门" prop="operationDeptId">
        <el-input v-model="queryParams.operationDeptId" placeholder="请选择运营部门" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>

      <el-form-item label="运营" prop="operationUserId">
        <el-input v-model="queryParams.operationUserId" placeholder="请选择运营" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>

      <el-form-item label="外卖店ID" prop="jdId">
        <el-input v-model="queryParams.jdId" placeholder="请输入外卖店ID" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>


      <el-form-item label="门店分类" prop="storeType">
        <el-select v-model="queryParams.storeType" placeholder="请选择门店分类" clearable class="!w-240px">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button @click="handSyncTest">
          <Icon icon="ep:search" class="mr-5px" /> 测试
        </el-button>

        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['system:store:create']">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading"
          v-hasPermi="['system:store:export']">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>

        <el-button plain type="warning" @click="handleImport">
          <Icon icon="ep:upload" />
          导入
        </el-button>
        <el-button type="danger" plain :disabled="isEmpty(checkedIds)" @click="handleDeleteBatch"
          v-hasPermi="['system:store:delete']">
          <Icon icon="ep:delete" class="mr-5px" /> 批量删除
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table row-key="id" v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true"
      @selection-change="handleRowCheckboxChange">
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="门店编码" align="center" prop="code" />
      <el-table-column label="品牌名称" align="center" prop="brandName" />
      <el-table-column label="门店名称" align="center" prop="name" />
      <el-table-column label="地区" align="center" prop="areaName" />
      <el-table-column label="详细地址" align="center" prop="address" />
      <el-table-column label="运营部门" align="center" prop="operationDeptName" />
      <el-table-column label="运营负责人" align="center" prop="operationUserName" />
      <el-table-column label="管理费率" align="center" prop="mgmtFeeRate" />
      <el-table-column label="京东外卖店ID" align="center" prop="jdId" />
      <el-table-column label="京东开业日期" align="center" prop="jdOpenDate" :formatter="dateFormatterYMD" />
      <el-table-column label="美团外卖店ID" align="center" prop="meituanId" />
      <el-table-column label="美团开业日期" align="center" prop="meituanOpenDate" :formatter="dateFormatterYMD" />
      <el-table-column label="饿了么外卖店ID" align="center" prop="eleId" />
      <el-table-column label="饿了么开业日期" align="center" prop="eleOpenDate" :formatter="dateFormatterYMD" />
      <el-table-column label="创建时间" align="center" prop="createTime" :formatter="dateFormatter" width="180px" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)" v-hasPermi="['system:store:update']">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)" v-hasPermi="['system:store:delete']">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <StoreForm ref="formRef" @success="getList" />
  <StoreImportForm ref="importFormRef" @success="getList" />
</template>

<script setup lang="ts">
import { isEmpty } from '@/utils/is'
import { dateFormatter, dateFormatterYMD } from '@/utils/formatTime'
import download from '@/utils/download'
import { StoreApi, StoreVO } from '@/api/system/store/base'
import StoreForm from './StoreForm.vue'

import StoreImportForm from './StoreImportForm.vue'

/** 门店信息 列表 */
defineOptions({ name: 'StoreBase' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const currentRow = ref({}) // 当前选中行
const list = ref<StoreVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  code: undefined,
  brandId: undefined,
  name: undefined,
  shortName: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  areaId: undefined,
  areaName: undefined,
  operationDeptId: undefined,
  operationDeptName: undefined,
  operationUserId: undefined,
  operationUserName: undefined,
  mgmtFeeRate: undefined,
  jdId: undefined,
  jdOpenDate: [],
  meituanId: undefined,
  meituanOpenDate: [],
  eleId: undefined,
  eleOpenDate: [],
  businessUserId: undefined,
  businessUserName: undefined,
  businessDeptId: undefined,
  businessDeptName: undefined,
  businessSignDate: [],
  orderSystemCode: undefined,
  orderSystemName: undefined,
  eleBusinessDistrict: undefined,
  meituanBusinessDistrict: undefined,
  jdBusinessDistrict: undefined,
  storeType: undefined,
  supervisionUserId: undefined,
  supervisionUserName: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

// 导入弹窗逻辑
const importFormRef = ref()
const handleImport = () => {
  importFormRef.value?.open()
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await StoreApi.getStorePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

const handSyncTest = async () => {
  try {
    // 从URL中获取shopId参数
    const urlParams = new URLSearchParams(window.location.search);
    const shopId = urlParams.get('shopId');

    // 将shopId传递给API
    await StoreApi.crawler(shopId || undefined);
    message.success('请求已发送');
  } catch (e) {
    message.error('请求失败');
  }
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await StoreApi.deleteStore(id)
    message.success(t('common.delSuccess'))
    currentRow.value = {}
    // 刷新列表
    await getList()
  } catch { }
}

/** 批量删除门店信息 */
const handleDeleteBatch = async () => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    await StoreApi.deleteStoreList(checkedIds.value);
    message.success(t('common.delSuccess'))
    await getList();
  } catch { }
}

const checkedIds = ref<number[]>([])
const handleRowCheckboxChange = (records: StoreVO[]) => {
  checkedIds.value = records.map((item) => item.id);
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await StoreApi.exportStore(queryParams)
    download.excel(data, '门店信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
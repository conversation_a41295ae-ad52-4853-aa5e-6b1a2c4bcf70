<template>
  <div class="dashboard-container">
    <!-- 顶部筛选条件 -->
    <ContentWrap>
      <div class="mb-4">
        <div class="bg-white rounded-lg p-4 shadow-sm border">
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-500">{{ lastUpdateTime }}</div>
            <div class="flex items-center space-x-8 flex-wrap gap-4">
              <!-- 运营部门选择 -->
              <div class="flex items-center space-x-2 min-w-[280px]">
                <span class="text-sm text-gray-600 whitespace-nowrap">运营部门:</span>
                <el-tree-select v-model="filterParams.deptId" :data="deptList"
                  :props="{ label: 'name', value: 'id', children: 'children' }" placeholder="全部部门" clearable
                  class="w-48" @change="handleFilterChange" check-strictly :render-after-expand="false"
                  :value-type="'number'" />
              </div>

              <!-- 人员选择 -->
              <div class="flex items-center space-x-2 min-w-[200px]">
                <span class="text-sm text-gray-600 whitespace-nowrap">人员:</span>
                <el-select v-model="filterParams.userId" placeholder="全部人员" clearable class="w-40"
                  @change="handleFilterChange">
                  <el-option v-for="user in userList" :key="user.id" :label="user.nickname" :value="user.id" />
                </el-select>
              </div>

              <!-- 品牌选择 -->
              <div class="flex items-center space-x-2 min-w-[200px]">
                <span class="text-sm text-gray-600 whitespace-nowrap">品牌:</span>
                <el-select v-model="filterParams.brandId" placeholder="全部品牌" clearable class="w-40"
                  @change="handleFilterChange">
                  <el-option v-for="brand in brandList" :key="brand.id" :label="brand.name" :value="brand.id" />
                </el-select>
              </div>

              <!-- 日期范围选择 -->
              <div class="flex items-center space-x-2 min-w-[280px]">
                <span class="text-sm text-gray-600 whitespace-nowrap">日期范围:</span>
                <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                  end-placeholder="结束日期" @change="handleDateChange" />
              </div>

              <!-- 城市选择 -->
              <div class="flex items-center space-x-2 min-w-[200px]">
                <span class="text-sm text-gray-600 whitespace-nowrap">城市:</span>
                <el-select v-model="filterParams.cityId" placeholder="全部城市" clearable class="w-40"
                  @change="handleFilterChange">
                  <el-option v-for="city in cityList" :key="city.id" :label="city.name" :value="city.id" />
                </el-select>
              </div>

              <!-- 门店选择 -->
              <div class="flex items-center space-x-2 min-w-[200px]">
                <span class="text-sm text-gray-600 whitespace-nowrap">门店:</span>
                <el-select v-model="filterParams.storeId" placeholder="全部门店" clearable class="w-40"
                  @change="handleFilterChange">
                  <el-option v-for="store in storeList" :key="store.id" :label="store.name" :value="store.id" />
                </el-select>
              </div>

              <!-- 分类选择 -->
              <div class="flex items-center space-x-2 min-w-[200px]">
                <span class="text-sm text-gray-600 whitespace-nowrap">分类:</span>
                <el-select v-model="filterParams.categoryId" placeholder="全部分类" clearable class="w-40"
                  @change="handleFilterChange">
                  <el-option v-for="category in categoryList" :key="category.id" :label="category.name"
                    :value="category.id" />
                </el-select>
              </div>

              <!-- 刷新按钮 -->
              <el-button type="primary" :icon="RefreshIcon" @click="refreshData" :loading="loading">
                刷新
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </ContentWrap>

    <!-- 数据表格 -->
    <ContentWrap>
      <div class="mb-4">
        <div class="p-4 border-b flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <h3 class="text-lg font-semibold">门店历史数据</h3>
            <!-- 标签切换 -->
            <el-segmented v-model="activeTab" :options="tabOptions" />
          </div>
        </div>

        <!-- 全部数据表格 -->
        <el-table v-if="activeTab === 'all'" v-loading="tableLoading" :data="allTableData" stripe style="width: 100%"
          :show-overflow-tooltip="true">
          <el-table-column prop="date" label="日期" align="center" />
          <el-table-column prop="code" label="编号" align="center" />
          <el-table-column prop="totalOrders" label="总单量" align="center" />
          <el-table-column prop="totalIncome" label="总收入" align="center" />
          <el-table-column prop="avgOrderValue" label="总客单" align="center" />
          <el-table-column prop="meituanOrders" label="美单量" align="center" />
          <el-table-column prop="meituanIncome" label="美收入" align="center" />
          <el-table-column prop="meituanAvgValue" label="美客单" align="center" />
          <el-table-column prop="meituanRating" label="美评分" align="center" />
          <el-table-column prop="eleOrders" label="饿单量" align="center" />
          <el-table-column prop="eleIncome" label="饿收入" align="center" />
          <el-table-column prop="eleAvgValue" label="饿客单" align="center" />
          <el-table-column prop="eleRating" label="饿评分" align="center" />
          <el-table-column prop="jdOrders" label="京单量" align="center" />
          <el-table-column prop="jdIncome" label="京收入" align="center" />
          <el-table-column prop="jdAvgValue" label="京客单" align="center" />
          <el-table-column prop="jdRating" label="京评分" align="center" />
        </el-table>

        <!-- 美团数据表格 -->
        <el-table v-else-if="activeTab === 'meituan'" v-loading="tableLoading" :data="meituanTableData" stripe
          style="width: 100%" :show-overflow-tooltip="true">
          <el-table-column prop="date" label="日期" align="center" />
          <el-table-column prop="code" label="编号" align="center" />
          <el-table-column prop="orders" label="单量" align="center" />
          <el-table-column prop="income" label="收入" align="center" />
          <el-table-column prop="avgValue" label="客单" align="center" />
          <el-table-column prop="rating" label="评分" align="center" />
          <el-table-column prop="promotionCost" label="推广消耗" align="center" />
          <el-table-column prop="exposure" label="曝光" align="center" />
          <el-table-column prop="visitRate" label="进店率" align="center" />
          <el-table-column prop="newCustomerOrders" label="新客下单" align="center" />
          <el-table-column prop="repeatCustomerOrders" label="老客下单" align="center" />
          <el-table-column prop="repeat7Days" label="近7日复购" align="center" />
          <el-table-column prop="repeat30Days" label="近30日复购" align="center" />
          <el-table-column prop="actualPayment" label="实付" align="center" />
          <el-table-column prop="businessHours" label="营业时长" align="center" />
        </el-table>

        <!-- 饿了么数据表格 -->
        <el-table v-else-if="activeTab === 'ele'" v-loading="tableLoading" :data="eleTableData" stripe
          style="width: 100%" :show-overflow-tooltip="true">
          <el-table-column prop="date" label="日期" align="center" />
          <el-table-column prop="code" label="编号" align="center" />
          <el-table-column prop="orders" label="单量" align="center" />
          <el-table-column prop="income" label="收入" align="center" />
          <el-table-column prop="avgValue" label="客单" align="center" />
          <el-table-column prop="rating" label="评分" align="center" />
          <el-table-column prop="promotionCost" label="推广消耗" align="center" />
          <el-table-column prop="exposure" label="曝光" align="center" />
          <el-table-column prop="visitRate" label="进店率" align="center" />
          <el-table-column prop="newCustomerOrders" label="新客下单" align="center" />
          <el-table-column prop="repeatCustomerOrders" label="老客下单" align="center" />
          <el-table-column prop="repeat7Days" label="近7日复购" align="center" />
          <el-table-column prop="repeat30Days" label="近30日复购" align="center" />
          <el-table-column prop="actualPayment" label="实付" align="center" />
          <el-table-column prop="businessHours" label="营业时长" align="center" />
        </el-table>

        <!-- 京东数据表格 -->
        <el-table v-else v-loading="tableLoading" :data="jdTableData" stripe style="width: 100%"
          :show-overflow-tooltip="true">
          <el-table-column prop="date" label="日期" align="center" />
          <el-table-column prop="code" label="编号" align="center" />
          <el-table-column prop="orders" label="单量" align="center" />
          <el-table-column prop="income" label="收入" align="center" />
          <el-table-column prop="avgValue" label="客单" align="center" />
          <el-table-column prop="rating" label="评分" align="center" />
          <el-table-column prop="promotionCost" label="推广消耗" align="center" />
          <el-table-column prop="exposure" label="曝光" align="center" />
          <el-table-column prop="visitRate" label="进店率" align="center" />
          <el-table-column prop="newCustomerOrders" label="新客下单" align="center" />
          <el-table-column prop="repeatCustomerOrders" label="老客下单" align="center" />
          <el-table-column prop="repeat7Days" label="近7日复购" align="center" />
          <el-table-column prop="repeat30Days" label="近30日复购" align="center" />
          <el-table-column prop="actualPayment" label="实付" align="center" />
          <el-table-column prop="businessHours" label="营业时长" align="center" />
        </el-table>

        <!-- 分页组件 -->
        <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
          @pagination="getTableData" />
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { formatDate } from '@/utils/formatTime'
import { Refresh as RefreshIcon } from '@element-plus/icons-vue'
import { handleTree } from '@/utils/tree'
import { erpPriceInputFormatter } from '@/utils'
import * as DeptApi from '@/api/system/dept'
import * as UserApi from '@/api/system/user'
import * as BrandApi from '@/api/system/brand'
import * as CityApi from '@/api/operation/city'
import * as StoreApi from '@/api/operation/store'
import * as CategoryApi from '@/api/operation/category'
import * as HistoryDataApi from '@/api/operation/history'
import Pagination from '@/components/Pagination/src/Pagination.vue'
import { ContentWrap } from '@/components/ContentWrap'
import { useMessage } from '@/hooks/web/useMessage'

/** 门店历史数据 */
defineOptions({ name: 'OperationStoreHistory' })

const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中

// 响应式数据
const tableLoading = ref(false)
const activeTab = ref('all')
const lastUpdateTime = ref('')
const dateRange = ref([])
const total = ref(0)

// 查询参数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})

// 筛选条件
const filterParams = reactive({
  deptId: null,
  userId: null,
  brandId: null,
  cityId: null,
  storeId: null,
  categoryId: null,
  startDate: '',
  endDate: ''
})

function sanitizeFilterParams(params: any) {
  function toNull(val: any) {
    return (
      val === null ||
      val === undefined ||
      val === '' ||
      val === 'null' ||
      val === 'undefined'
    )
      ? null
      : Number(val)
  }
  return {
    ...params,
    deptId: toNull(params.deptId),
    userId: toNull(params.userId),
    brandId: toNull(params.brandId),
    cityId: toNull(params.cityId),
    storeId: toNull(params.storeId),
    categoryId: toNull(params.categoryId)
  }
}

// 标签选项
const tabOptions = [
  { label: '全部', value: 'all' },
  { label: '美团', value: 'meituan' },
  { label: '饿了么', value: 'ele' },
  { label: '京东', value: 'jd' }
]

// 下拉选择框数据
const deptList = ref<DeptApi.DeptVO[]>([])
const userList = ref<UserApi.UserVO[]>([])
const brandList = ref<BrandApi.brandVO[]>([])
const cityList = ref<CityApi.CityVO[]>([])
const storeList = ref<StoreApi.StoreVO[]>([])
const categoryList = ref<CategoryApi.CategoryVO[]>([])

// 表格数据
const allTableData = ref<HistoryDataApi.StoreHistoryData[]>([])
const meituanTableData = ref<HistoryDataApi.PlatformHistoryData[]>([])
const eleTableData = ref<HistoryDataApi.PlatformHistoryData[]>([])
const jdTableData = ref<HistoryDataApi.PlatformHistoryData[]>([])

/** 获取筛选选项数据 */
const getFilterOptions = async () => {
  try {
    // 获取部门列表
    const depts = await DeptApi.getSimpleDeptList()
    deptList.value = handleTree(depts)

    // 获取用户列表
    const sanitized = sanitizeFilterParams(filterParams)
    userList.value = sanitized.deptId ? await UserApi.getSimpleUserListByDeptId(sanitized.deptId) : await UserApi.getSimpleUserList()

    // 获取品牌列表
    brandList.value = await BrandApi.getSimpleBrandList()

    // 获取城市列表
    cityList.value = await CityApi.getSimpleCityList()

    // 获取门店列表
    storeList.value = await StoreApi.getSimpleStoreList()

    // 获取分类列表
    categoryList.value = await CategoryApi.getSimpleCategoryList()
  } catch (error) {
    console.error('获取筛选选项失败:', error)
  }
}

/** 监听部门变化，更新用户列表 */
watch(() => filterParams.deptId, async (newDeptId) => {
  try {
    if (newDeptId) {
      userList.value = await UserApi.getSimpleUserListByDeptId(newDeptId)
    } else {
      userList.value = await UserApi.getSimpleUserList()
    }
    // 清空用户选择
    filterParams.userId = null
  } catch (error) {
    console.error('获取用户列表失败:', error)
  }
})

/** 处理日期变化 */
const handleDateChange = (dates: any) => {
  if (dates && dates.length === 2) {
    filterParams.startDate = formatDate(dates[0], 'YYYY-MM-DD')
    filterParams.endDate = formatDate(dates[1], 'YYYY-MM-DD')
  } else {
    filterParams.startDate = ''
    filterParams.endDate = ''
  }
  refreshData()
}

/** 筛选条件变化处理 */
const handleFilterChange = () => {
  queryParams.pageNo = 1 // 重置页码
  refreshData()
}

/** 获取全部数据 */
const getAllData = async () => {
  try {
    const params = { ...sanitizeFilterParams(filterParams), ...queryParams }
    const data = await HistoryDataApi.getAllStoreHistoryData(params)
    if (data) {
      allTableData.value = data.list || []
      total.value = data.total || 0
    }
  } catch (error) {
    console.error('获取全部数据失败:', error)
    message.error('获取全部数据失败')
  }
}

/** 获取美团数据 */
const getMeituanData = async () => {
  try {
    const params = { ...sanitizeFilterParams(filterParams), ...queryParams }
    const data = await HistoryDataApi.getMeituanStoreHistoryData(params)
    if (data) {
      meituanTableData.value = data.list || []
      total.value = data.total || 0
    }
  } catch (error) {
    console.error('获取美团数据失败:', error)
    message.error('获取美团数据失败')
  }
}

/** 获取饿了么数据 */
const getEleData = async () => {
  try {
    const params = { ...sanitizeFilterParams(filterParams), ...queryParams }
    const data = await HistoryDataApi.getEleStoreHistoryData(params)
    if (data) {
      eleTableData.value = data.list || []
      total.value = data.total || 0
    }
  } catch (error) {
    console.error('获取饿了么数据失败:', error)
    message.error('获取饿了么数据失败')
  }
}

/** 获取京东数据 */
const getJdData = async () => {
  try {
    const params = { ...sanitizeFilterParams(filterParams), ...queryParams }
    const data = await HistoryDataApi.getJdStoreHistoryData(params)
    if (data) {
      jdTableData.value = data.list || []
      total.value = data.total || 0
    }
  } catch (error) {
    console.error('获取京东数据失败:', error)
    message.error('获取京东数据失败')
  }
}

/** 获取表格数据 */
const getTableData = async () => {
  tableLoading.value = true
  try {
    if (activeTab.value === 'all') {
      await getAllData()
    } else if (activeTab.value === 'meituan') {
      await getMeituanData()
    } else if (activeTab.value === 'ele') {
      await getEleData()
    } else {
      await getJdData()
    }
  } finally {
    tableLoading.value = false
  }
}

/** 刷新数据 */
const refreshData = async () => {
  try {
    await Promise.all([
      getFilterOptions(),
      getTableData()
    ])
    lastUpdateTime.value = formatDate(new Date(), 'YYYY-MM-DD HH:mm')
    message.success('数据已刷新')
  } catch (error) {
    console.error('刷新数据失败:', error)
    message.error('刷新数据失败')
  }
}

// 监听标签切换
watch(activeTab, () => {
  getTableData()
})

/** 初始化 */
onMounted(async () => {
  loading.value = true
  try {
    await refreshData()
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
.dashboard-container {
  padding: 0;
}

.el-table {
  margin-bottom: 20px;
}

.el-segmented {
  margin-left: 10px;
}
</style>
# 运营管理模块 (Operation Module)

## 模块概述

运营管理模块是一个完整的业务模块，用于管理和展示运营相关的历史数据。该模块按照system模块的架构模式进行设计，提供了完整的数据查询、筛选和展示功能。

## 功能特性

### 1. 历史数据管理
- **门店历史数据**: 提供门店运营数据的历史查询和分析功能

### 2. 多维度筛选
- **运营部门**: 支持按部门层级筛选
- **人员选择**: 支持按具体运营人员筛选
- **品牌选择**: 支持按品牌筛选
- **日期范围**: 支持自定义日期范围查询
- **城市选择**: 支持按城市筛选
- **门店选择**: 支持按具体门店筛选
- **分类选择**: 支持按业务分类筛选

### 3. 多平台数据展示
- **全部数据**: 汇总展示所有平台的数据
- **美团数据**: 专门展示美团平台的详细数据
- **饿了么数据**: 专门展示饿了么平台的详细数据
- **京东数据**: 专门展示京东平台的详细数据

### 4. 数据表格功能
- **分页支持**: 所有列表都支持分页显示
- **实时刷新**: 支持手动刷新数据
- **响应式设计**: 适配不同屏幕尺寸

## 技术架构

### 1. 前端技术栈
- **Vue 3**: 使用Composition API
- **TypeScript**: 提供类型安全
- **Element Plus**: UI组件库
- **Vite**: 构建工具

### 2. 路由配置
```typescript
// src/router/modules/operation.ts
const operation: AppRouteModule = {
  path: '/operation',
  name: 'Operation',
  component: LAYOUT,
  redirect: '/operation/history',
  meta: {
    title: '运营管理',
    icon: 'ion:settings-outline'
  },
  children: [
    {
      path: 'history',
      name: 'HistoryData',
      component: LAYOUT,
      redirect: '/operation/history/store',
      meta: {
        title: '历史数据',
        icon: 'ion:time-outline'
      },
      children: [
        {
          path: 'store',
          name: 'OperationStoreHistory',
          component: () => import('@/views/operation/history/store/index.vue'),
          meta: {
            title: '门店历史数据',
            icon: 'ion:storefront-outline'
          }
        }
      ]
    }
  ]
}
```

### 3. API接口设计
```typescript
// 历史数据查询参数
export interface HistoryQueryParams extends BaseParams {
  deptId?: number | null
  userId?: number | null
  brandId?: number | null
  cityId?: number | null
  storeId?: number | null
  categoryId?: number | null
  startDate?: string
  endDate?: string
  pageNo?: number
  pageSize?: number
}
```

## 文件结构

```
src/views/operation/
├── README.md                    # 模块说明文档
└── history/                     # 历史数据模块
    └── store/                   # 门店历史数据
        └── index.vue           # 门店历史数据页面

src/api/operation/
├── history.ts                   # 历史数据API
├── city.ts                     # 城市API
├── store.ts                    # 门店API
└── category.ts                 # 分类API

src/router/modules/
└── operation.ts                # 运营模块路由配置
```

## 数据表格字段说明

### 全部数据表格字段
- 日期、编号、总单量、总收入、总客单
- 美单量、美收入、美客单、美评分
- 饿单量、饿收入、饿客单、饿评分
- 京单量、京收入、京客单、京评分

### 平台数据表格字段（美团/饿了么/京东）
- 日期、编号、单量、收入、客单、评分
- 推广消耗、曝光、进店率
- 新客下单、老客下单
- 近7日复购、近30日复购
- 实付、营业时长

## 使用说明

### 1. 访问路径
```
/operation/history/store
```

### 2. 功能操作
1. **筛选数据**: 使用顶部筛选条件进行数据筛选
2. **切换平台**: 使用标签切换查看不同平台的数据
3. **分页浏览**: 使用底部分页组件浏览大量数据
4. **刷新数据**: 点击刷新按钮获取最新数据

### 3. 筛选联动
- 选择部门后，人员列表会自动更新为该部门下的人员
- 所有筛选条件变化时，会自动重置到第一页并刷新数据

## 扩展说明

该模块采用模块化设计，可以方便地扩展新的功能：

1. **新增数据类型**: 在history目录下创建新的子模块
2. **新增筛选条件**: 在API接口中添加新的查询参数
3. **新增数据源**: 创建新的API文件和对应的组件

## 注意事项

1. 所有API接口都需要后端提供对应的实现
2. 分页功能依赖后端返回标准的分页数据格式
3. 筛选条件的联动需要前端和后端配合实现
4. 日期范围选择器的格式为 'YYYY-MM-DD'
